﻿@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@inject ICosigningService CosigningService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningComponent> Logger
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.False" Class="cosigning-container">
    @if (ShowCosigningSection)
    {
        <MudPaper Class="cosigning-paper" Elevation="2">
            <!-- Header Section -->
            <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween" Class="cosigning-header">
                <MudItem>
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser"
                                 Color="Color.Primary"
                                 Size="Size.Medium" />
                        <MudText Typo="Typo.h6" Class="header-title">
                            @Localizer["DocumentSignature"]
                        </MudText>
                    </MudStack>
                </MudItem>
                <MudItem>
                    <MudChip T="string"
                             Size="Size.Small"
                             Color="@GetStatusColor()"
                             Variant="Variant.Filled"
                             Class="status-chip">
                        @GetStatusText()
                    </MudChip>
                </MudItem>
            </MudGrid>

            <!-- Inline Signing Form (shown when signing is initiated) -->
            @if (_showSigningForm)
            {
                <MudPaper Class="signature-form-block" Elevation="2" Style="margin-bottom: 16px; padding: 16px; border: 1px solid #1976d2; background: #f8f9fa;">
                    <MudStack Spacing="3">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" Size="Size.Small" />
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600; color: #1976d2;">
                                @Localizer["DocumentSigning"]
                            </MudText>
                            <MudSpacer />
                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                           Size="Size.Small"
                                           OnClick="CancelSigning"
                                           Style="color: #666;" />
                        </MudStack>

                        <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                            <MudText Typo="Typo.body2" Style="font-weight: 500;">@Localizer["Select Signing Type"]:</MudText>
                            <MudRadioGroup @bind-Value="_isCoSignMode" Row="true">
                                <MudRadio Value="false" Color="Color.Primary" Dense="true">@Localizer["Sign"]</MudRadio>
                                <MudRadio Value="true" Color="Color.Primary" Dense="true" Disabled="@(!CurrentCosigning.IsSigned)">@Localizer["CoSign"]</MudRadio>
                            </MudRadioGroup>
                        </MudStack>

                        @if (_isCoSignMode)
                        {
                            <MudSelect @bind-Value="_selectedCosigner"
                                       Label="@Localizer["SelectCosigner"]"
                                       Variant="Variant.Outlined"
                                       Size="Size.Small"
                                       Style="max-width: 300px;">
                                @foreach (var provider in _providerList)
                                {
                                    <MudSelectItem Value="@provider">@provider</MudSelectItem>
                                }
                            </MudSelect>
                        }

                        <MudStack Row Justify="Justify.FlexEnd" Spacing="2">                            
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="ProcessSignature"                                       
                                       Size="Size.Small"
                                       StartIcon="@Icons.Material.Filled.Draw">
                                @Localizer["Add"]
                            </MudButton>
                        </MudStack>
                    </MudStack>
                </MudPaper>
            }

            <!-- Signature Section -->
            <MudStack Class="signature-section" Spacing="3">
                
                
                    <!-- Primary Signature Display -->
                    <MudTextField Variant="Variant.Outlined"                                  
                                  Value="@GetSignatureText()"
                                  ReadOnly="true"
                                  FullWidth="true"
                                  Lines="3"
                                  Multiline="true"
                                  Style="font-family: monospace; background: #f8fff8;" />

                    <MudStack Row Justify="Justify.FlexEnd" Spacing="2" Style="margin-top: 8px;">
                        
                        
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Secondary"
                                       StartIcon="@Icons.Material.Filled.SupervisorAccount"
                                       OnClick="ShowSigningForm"
                                       Disabled="@IsProcessing"
                                       Size="Size.Small"
                                       Class="signature-button">
                                @Localizer["Sign"]
                            </MudButton>
                        

                        @if (!CurrentCosigning.IsLocked && (CurrentCosigning.IsSigned && (!RequiresCosignature || CurrentCosigning.IsCosigned)))
                        {
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Warning"
                                       StartIcon="@Icons.Material.Filled.Lock"
                                       OnClick="LockDocument"
                                       Disabled="@IsProcessing"
                                       Size="Size.Small"
                                       Class="signature-button">
                                @Localizer["Lock"]
                            </MudButton>
                        }
                    </MudStack>
                
            </MudStack>

            <!-- Lock Status -->
            @if (CurrentCosigning.IsLocked)
            {
                <MudAlert Severity="Severity.Info"
                          Variant="Variant.Filled"
                          Dense="true"
                          Class="lock-alert">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                        <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Small" />
                        <MudText>@Localizer["DocumentLocked"]</MudText>
                    </MudStack>
                </MudAlert>
            }
        </MudPaper>
    }
</MudContainer>

<style>
    .cosigning-container {
        padding: 0;
        margin: 0;
        width: 100%;
    }

    .cosigning-paper {
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        margin-bottom: 16px;
        width: 100%;
    }

    .cosigning-header {
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    .header-title {
        font-weight: 600;
        color: #1976d2;
        margin: 0;
        font-size: 1rem;
    }

    .status-chip {
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.75rem;
    }

    .signature-section {
        margin-top: 0;
    }

    .signature-block {
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        transition: all 0.2s ease;
    }

    .signature-block:hover {
        border-color: #1976d2;
        box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
    }

    .signature-block.signed {
        background: #f8fff8;
        border-color: #4caf50;
    }

    .signature-title {
        font-weight: 600;
        color: #333;
        margin: 0;
        font-size: 0.875rem;
    }

    .signature-title.signed {
        color: #2e7d32;
    }

    .signature-button {
        min-width: 120px;
        font-weight: 500;
        text-transform: none;
        border-radius: 4px;
        font-size: 0.875rem;
    }

    .signature-divider {
        margin: 12px 0;
        opacity: 0.6;
    }

    .lock-alert {
        margin-top: 12px;
        border-radius: 4px;
    }
</style>