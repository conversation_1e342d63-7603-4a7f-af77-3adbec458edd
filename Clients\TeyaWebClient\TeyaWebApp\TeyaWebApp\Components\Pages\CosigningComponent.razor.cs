﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;


namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Professional cosigning component for document signature workflow
    /// Designed with Nabla-inspired UI/UX principles
    /// </summary>
    public partial class CosigningComponent
    {
        /// <summary>
        /// Current active user for authentication context
        /// </summary>
        [Inject] private ActiveUser CurrentUser { get; set; } 

        /// <summary>
        /// Navigation manager for page navigation
        /// </summary>
        [Inject] private NavigationManager NavigationManager { get; set; } 

        /// <summary>
        /// Member service for getting provider list
        /// </summary>
        [Inject] private IMemberService MemberService { get; set; } 

        /// <summary>
        /// The record ID for which cosigning is being performed
        /// </summary>
        [Parameter] public Guid RecordId { get; set; }

        /// <summary>
        /// The patient ID associated with the record
        /// </summary>
        [Parameter] public Guid PatientId { get; set; }

        /// <summary>
        /// The patient name to display in the component
        /// </summary>
        [Parameter] public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// The organization ID for the cosigning workflow
        /// </summary>
        [Parameter] public Guid OrganizationId { get; set; }

        /// <summary>
        /// Whether to show the cosigning section
        /// </summary>
        [Parameter] public bool ShowCosigningSection { get; set; } = true;

        /// <summary>
        /// Whether the document requires a cosignature
        /// </summary>
        [Parameter] public bool RequiresCosignature { get; set; } = false;

        /// <summary>
        /// Event callback fired when signature is updated
        /// </summary>
        [Parameter] public EventCallback<Cosigning> OnSignatureUpdated { get; set; }

        /// <summary>
        /// Current cosigning state
        /// </summary>
        private Cosigning CurrentCosigning { get; set; } = new();

        /// <summary>
        /// Flag to track if the current cosigning record exists in the database
        /// </summary>
        private bool IsNewRecord { get; set; } = true;

        /// <summary>
        /// Whether a signing operation is in progress
        /// </summary>
        private bool IsProcessing { get; set; } = false;
        private bool Subscription { get; set; } = false;

        // Inline signing form properties
        private bool _showSigningForm = false;
        private bool _isCoSignMode = false;
        private Member? _selectedCosigner = null;
        private List<string> _providerList = new List<string>();

        /// <summary>
        /// Initialize the component and load cosigning status
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadCosigningStatus();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing cosigning component for record {RecordId}", RecordId);
            }
        }

        /// <summary>
        /// Handle parameter changes and reload cosigning status if needed
        /// </summary>
        protected override async Task OnParametersSetAsync()
        {
            if (RecordId != Guid.Empty)
            {
                await LoadCosigningStatus();
            }
        }

        /// <summary>
        /// Load the current cosigning status from the backend
        /// </summary>
        private async Task LoadCosigningStatus()
        {
            try
            {
                Logger.LogInformation("Loading cosigning status for record {RecordId}", RecordId);

                var cosignings = await CosigningService.GetCosigningsByRecordId(RecordId);
                var existingCosigning = cosignings?.OrderByDescending(c => c.Date).FirstOrDefault();

                if (existingCosigning != null)
                {
                    CurrentCosigning = existingCosigning;
                    IsProcessing = CurrentCosigning.IsLocked;
                    IsNewRecord = false;
                    Logger.LogInformation("Found existing cosigning record for {RecordId}. IsSigned: {IsSigned}, IsCosigned: {IsCosigned}",
                        RecordId, CurrentCosigning.IsSigned, CurrentCosigning.IsCosigned);
                }
                else
                {
                    // Initialize with default values - don't assign ID yet
                    CurrentCosigning = new Cosigning
                    {
                        RecordId = RecordId,
                        OrganizationId = OrganizationId,
                        IsSigned = false,
                        IsCosigned = false,
                        IsLocked = false
                    };
                    IsNewRecord = true;
                    Logger.LogInformation("No existing cosigning record found for {RecordId}. Initialized new record.", RecordId);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading cosigning status for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLoadingSignatureStatus"], Severity.Error);

                // Initialize with default values on error
                CurrentCosigning = new Cosigning
                {
                    RecordId = RecordId,
                    OrganizationId = OrganizationId,
                    IsSigned = false,
                    IsCosigned = false,
                    IsLocked = false
                };
                IsNewRecord = true;
            }
        }

        /// <summary>
        /// Handle primary document signing
        /// </summary>
        private async Task SignDocument()
        {

            try
            {
                Logger.LogInformation("Signing document for record {RecordId}", RecordId);

                // Validate required fields
                if (!Guid.TryParse(CurrentUser.id, out var signerId) || signerId == Guid.Empty)
                {
                    Logger.LogError("Invalid user ID for signing: {UserId}", CurrentUser.id);
                    Snackbar.Add(Localizer["InvalidUserForSigning"], Severity.Error);
                    return;
                }

                var signerName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";
                if (string.IsNullOrWhiteSpace(signerName) || signerName == "Unknown User")
                {
                    Logger.LogError("Invalid user name for signing: {UserName}", signerName);
                    Snackbar.Add(Localizer["InvalidUserNameForSigning"], Severity.Error);
                    return;
                }

                CurrentCosigning.IsSigned = true;
                CurrentCosigning.SignerId = signerId;
                CurrentCosigning.SignerName = signerName;
                CurrentCosigning.Date = DateTime.UtcNow;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                if (IsNewRecord)
                {
                    CurrentCosigning.Id = Guid.NewGuid();
                    await CosigningService.AddCosigning(new List<Cosigning> { CurrentCosigning });
                    IsNewRecord = false;
                }
                else
                {
                    await CosigningService.UpdateCosigning(CurrentCosigning);
                }

                Logger.LogInformation("Document signed successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentSignedSuccessfully"], Severity.Success);

                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error signing document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorSigningDocument"], Severity.Error);
            }

        }

        /// <summary>
        /// Handle document cosigning
        /// </summary>
        private async Task CosignDocument()
        {

            try
            {
                Logger.LogInformation("Cosigning document for record {RecordId}", RecordId);

                // Validate required fields
                if (!Guid.TryParse(CurrentUser.id, out var cosignerId) || cosignerId == Guid.Empty)
                {
                    Logger.LogError("Invalid user ID for cosigning: {UserId}", CurrentUser.id);
                    Snackbar.Add(Localizer["InvalidUserForCosigning"], Severity.Error);
                    return;
                }

                var cosignerName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";
                if (string.IsNullOrWhiteSpace(cosignerName) || cosignerName == "Unknown User")
                {
                    Logger.LogError("Invalid user name for cosigning: {UserName}", cosignerName);
                    Snackbar.Add(Localizer["InvalidUserNameForCosigning"], Severity.Error);
                    return;
                }

                CurrentCosigning.IsCosigned = true;
                CurrentCosigning.CosignerId = cosignerId;
                CurrentCosigning.CosignerName = cosignerName;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                await CosigningService.UpdateCosigning(CurrentCosigning);

                Logger.LogInformation("Document cosigned successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentCosignedSuccessfully"], Severity.Success);

                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error cosigning document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorCosigningDocument"], Severity.Error);
            }

        }

        /// <summary>
        /// Get the appropriate color for the status chip
        /// </summary>
        private Color GetStatusColor()
        {
            if (CurrentCosigning.IsLocked) return Color.Warning;
            if (CurrentCosigning.IsCosigned) return Color.Success;
            if (CurrentCosigning.IsSigned) return Color.Info;
            return Color.Default;
        }

        /// <summary>
        /// Get the status text for the status chip
        /// </summary>
        private string GetStatusText()
        {
            if (CurrentCosigning.IsLocked) return Localizer["Locked"];
            if (CurrentCosigning.IsCosigned) return Localizer["Cosigned"];
            if (CurrentCosigning.IsSigned) return Localizer["Signed"];
            return Localizer["PendingSignature"];
        }

        /// <summary>
        /// Public method to refresh the signature status
        /// Can be called from parent components
        /// </summary>
        public async Task RefreshSignatureStatus()
        {
            await LoadCosigningStatus();
            StateHasChanged();
        }

        /// <summary>
        /// Check if the component should be visible based on current state
        /// </summary>
        public bool ShouldShowComponent()
        {
            return ShowCosigningSection && RecordId != Guid.Empty;
        }

        /// <summary>
        /// Get the current signature completion percentage for progress indicators
        /// </summary>
        public int GetSignatureProgress()
        {
            if (CurrentCosigning.IsLocked) return 100;
            if (CurrentCosigning.IsCosigned) return 100;
            if (CurrentCosigning.IsSigned && !RequiresCosignature) return 100;
            if (CurrentCosigning.IsSigned && RequiresCosignature) return 75;
            return 0;
        }

        /// <summary>
        /// Show the inline signing form
        /// </summary>
        private async Task ShowSigningForm()
        {
            try
            {
                // Load provider list for cosigning
                await LoadProviderList();

                // Set default mode based on current state
                _isCoSignMode = CurrentCosigning.IsSigned;
                _selectedCosigner = null;
                _showSigningForm = true;

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error showing signing form for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorShowingSigningForm"], Severity.Error);
            }
        }

        /// <summary>
        /// Cancel the signing process
        /// </summary>
        private void CancelSigning()
        {
            _showSigningForm = false;
            _isCoSignMode = false;
            _selectedCosigner = null;
            StateHasChanged();
        }

        /// <summary>
        /// Process the signature (sign or cosign)
        /// </summary>
        private async Task ProcessSignature()
        {

            try
            {
                if (_isCoSignMode)
                {
                    await ProcessCosignature();
                }
                else
                {
                    await ProcessDocumentSigning();
                }

                _showSigningForm = false;
                await LoadCosigningStatus();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing signature for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorProcessingSignature"], Severity.Error);
            }

        }

        /// <summary>
        /// Process document signing
        /// </summary>
        private async Task ProcessDocumentSigning()
        {
            Logger.LogInformation("Signing document for record {RecordId}", RecordId);

            // Validate required fields
            if (!Guid.TryParse(CurrentUser.id, out var signerId) || signerId == Guid.Empty)
            {
                Logger.LogError("Invalid user ID for signing: {UserId}", CurrentUser.id);
                Snackbar.Add(Localizer["InvalidUserForSigning"], Severity.Error);
                return;
            }

            var signerName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";
            if (string.IsNullOrWhiteSpace(signerName) || signerName == "Unknown User")
            {
                Logger.LogError("Invalid user name for signing: {UserName}", signerName);
                Snackbar.Add(Localizer["InvalidUserNameForSigning"], Severity.Error);
                return;
            }

            var now = DateTime.UtcNow;

            CurrentCosigning.IsSigned = true;
            CurrentCosigning.SignerId = signerId;
            CurrentCosigning.SignerName = signerName;
            CurrentCosigning.Date = now;
            CurrentCosigning.IsCosigned = false;
            CurrentCosigning.IsLocked = false;
            CurrentCosigning.CosignerName = string.Empty;
            CurrentCosigning.OrganizationId = OrganizationId;
            CurrentCosigning.LastUpdated = now;
            CurrentCosigning.Notes = string.Empty;

            if (IsNewRecord)
            {
                CurrentCosigning.Id = Guid.NewGuid();
                await CosigningService.AddCosigning(new List<Cosigning> { CurrentCosigning });
                IsNewRecord = false;
            }
            else
            {
                await CosigningService.UpdateCosigning(CurrentCosigning);
            }

            Logger.LogInformation("Document signed successfully for record {RecordId}", RecordId);
            Snackbar.Add(Localizer["DocumentSignedSuccessfully"], Severity.Success);
        }

        /// <summary>
        /// Process document cosigning
        /// </summary>
        /// <summary>
        /// Process document cosigning
        /// </summary>
        private async Task ProcessCosignature()
        {
            if (_selectedCosigner == null)
            {
                Snackbar.Add(Localizer["PleaseSelectCosigner"], Severity.Warning);
                return;
            }

            if (!CurrentCosigning.IsSigned)
            {
                Snackbar.Add(Localizer["DocumentMustBeSignedFirst"], Severity.Warning);
                return;
            }

            if (IsNewRecord)
            {
                Snackbar.Add(Localizer["DocumentMustBeSignedFirst"], Severity.Warning);
                return;
            }

            Logger.LogInformation("Cosigning document for record {RecordId}", RecordId);

            try
            {
                CurrentCosigning.IsCosigned = true;
                CurrentCosigning.CosignerId = _selectedCosigner.Id;
                CurrentCosigning.CosignerName = _selectedCosigner.UserName;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;
                CurrentCosigning.OrganizationId = OrganizationId;

                await CosigningService.UpdateCosigning(CurrentCosigning);

                Logger.LogInformation("Document cosigned successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentCosignedSuccessfully"], Severity.Success);

                // Trigger the callback to notify parent component
                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error during cosigning process for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorCosigningDocument"], Severity.Error);
                throw; // Re-throw to be handled by ProcessSignature method
            }
        }

        /// <summary>
        /// Lock the document
        /// </summary>
        private async Task LockDocument()
        {

            try
            {
                Logger.LogInformation("Locking document for record {RecordId}", RecordId);

                CurrentCosigning.IsLocked = true;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                await CosigningService.UpdateCosigning(CurrentCosigning);

                Logger.LogInformation("Document locked successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentLockedSuccessfully"], Severity.Success);

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error locking document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLockingDocument"], Severity.Error);
            }

        }

        /// <summary>
        /// Get the signature text for display in the primary signature field
        /// </summary>
        private string GetSignatureText()
        {
            var lines = new List<string>();

            if (CurrentCosigning.IsSigned)
            {
                lines.Add($"Electronically Signed by {CurrentCosigning.SignerName} on {CurrentCosigning.Date:MM/dd/yyyy}");
            }

            if (CurrentCosigning.IsCosigned)
            {
                lines.Add($"Electronically Cosigned by {CurrentCosigning.CosignerName} on {CurrentCosigning.LastUpdated:MM/dd/yyyy}");
            }

            if (CurrentCosigning.IsLocked)
            {
                lines.Add("Notes Locked!!");
            }

            return string.Join("\n", lines);
        }

        /// <summary>
        /// Load the provider list for cosigning
        /// </summary>
        private async Task LoadProviderList()
        {
            try
            {
                _providerList = await MemberService.GetProviderlistAsync(OrganizationId, Subscription);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading provider list for organization {OrganizationId}", OrganizationId);
            }
        }
    }
}