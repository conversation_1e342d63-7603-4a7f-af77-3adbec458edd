﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;


namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Professional cosigning component for document signature workflow
    /// Designed with Nabla-inspired UI/UX principles
    /// </summary>
    public partial class CosigningComponent
    {
        /// <summary>
        /// Current active user for authentication context
        /// </summary>
        [Inject] private ActiveUser CurrentUser { get; set; } 

        /// <summary>
        /// Navigation manager for page navigation
        /// </summary>
        [Inject] private NavigationManager NavigationManager { get; set; } 

        /// <summary>
        /// Member service for getting provider list
        /// </summary>
        [Inject] private IMemberService MemberService { get; set; } 

        /// <summary>
        /// The record ID for which cosigning is being performed
        /// </summary>
        [Parameter] public Guid RecordId { get; set; }

        /// <summary>
        /// The patient ID associated with the record
        /// </summary>
        [Parameter] public Guid PatientId { get; set; }

        /// <summary>
        /// The patient name to display in the component
        /// </summary>
        [Parameter] public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// The organization ID for the cosigning workflow
        /// </summary>
        [Parameter] public Guid OrganizationId { get; set; }

        /// <summary>
        /// Whether to show the cosigning section
        /// </summary>
        [Parameter] public bool ShowCosigningSection { get; set; } = true;

        /// <summary>
        /// Whether the document requires a cosignature
        /// </summary>
        [Parameter] public bool RequiresCosignature { get; set; } = false;

        /// <summary>
        /// Event callback fired when signature is updated
        /// </summary>
        [Parameter] public EventCallback<Cosigning> OnSignatureUpdated { get; set; }

        /// <summary>
        /// Current cosigning state
        /// </summary>
        private Cosigning CurrentCosigning { get; set; } = new();

        /// <summary>
        /// Flag to track if the current cosigning record exists in the database
        /// </summary>
        private bool IsNewRecord { get; set; } = true;

        /// <summary>
        /// Whether a signing operation is in progress
        /// </summary>
        private bool IsProcessing { get; set; } = false;
        private bool Subscription { get; set; } = false;

        // Inline signing form properties
        private bool _showSigningForm = false;
        private bool _isCoSignMode = false;
        private Member? _selectedCosigner = null;
        private List<Member> _providerList = new List<Member>();

        /// <summary>
        /// Initialize the component and load cosigning status
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadCosigningStatus();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing cosigning component for record {RecordId}", RecordId);
            }
        }

        /// <summary>
        /// Handle parameter changes and reload cosigning status if needed
        /// </summary>
        protected override async Task OnParametersSetAsync()
        {
            try
            {
                // Only reload if we have a valid record ID
                if (RecordId != Guid.Empty)
                {
                    // Reset state when record ID changes
                    _showSigningForm = false;
                    _isCoSignMode = false;
                    _selectedCosigner = null;

                    // Load the current cosigning status
                    await LoadCosigningStatus();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error in OnParametersSetAsync for record {RecordId}", RecordId);
            }
        }

        /// <summary>
        /// Load the current cosigning status from the backend
        /// </summary>
        private async Task LoadCosigningStatus()
        {
            try
            {
                // Don't proceed if RecordId is empty
                if (RecordId == Guid.Empty)
                {
                    Logger.LogWarning("Cannot load cosigning status: RecordId is empty");
                    return;
                }

                Logger.LogInformation("Loading cosigning status for record {RecordId}", RecordId);

                // Get all cosigning records for this record
                var cosignings = await CosigningService.GetCosigningsByRecordId(RecordId);

                // Get the most recent cosigning record
                var existingCosigning = cosignings?.OrderByDescending(c => c.LastUpdated).FirstOrDefault();

                if (existingCosigning != null)
                {
                    // Use the existing record
                    CurrentCosigning = existingCosigning;
                    IsProcessing = CurrentCosigning.IsLocked;
                    IsNewRecord = false;

                    Logger.LogInformation("Found existing cosigning record for {RecordId}. ID: {Id}, IsSigned: {IsSigned}, IsCosigned: {IsCosigned}, IsLocked: {IsLocked}",
                        RecordId, CurrentCosigning.Id, CurrentCosigning.IsSigned, CurrentCosigning.IsCosigned, CurrentCosigning.IsLocked);
                }
                else
                {
                    // Initialize with default values - don't assign ID yet
                    CurrentCosigning = new Cosigning
                    {
                        RecordId = RecordId,
                        OrganizationId = OrganizationId,
                        IsSigned = false,
                        IsCosigned = false,
                        IsLocked = false,
                        Date = DateTime.UtcNow,
                        LastUpdated = DateTime.UtcNow
                    };
                    IsNewRecord = true;
                    IsProcessing = false;

                    Logger.LogInformation("No existing cosigning record found for {RecordId}. Initialized new record.", RecordId);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading cosigning status for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLoadingSignatureStatus"], Severity.Error);

                // Initialize with default values on error
                CurrentCosigning = new Cosigning
                {
                    RecordId = RecordId,
                    OrganizationId = OrganizationId,
                    IsSigned = false,
                    IsCosigned = false,
                    IsLocked = false,
                    Date = DateTime.UtcNow,
                    LastUpdated = DateTime.UtcNow
                };
                IsNewRecord = true;
                IsProcessing = false;
            }
        }

        /// <summary>
        /// Handle primary document signing
        /// </summary>
        private async Task SignDocument()
        {

            try
            {
                Logger.LogInformation("Signing document for record {RecordId}", RecordId);

                // Validate required fields
                if (!Guid.TryParse(CurrentUser.id, out var signerId) || signerId == Guid.Empty)
                {
                    Logger.LogError("Invalid user ID for signing: {UserId}", CurrentUser.id);
                    Snackbar.Add(Localizer["InvalidUserForSigning"], Severity.Error);
                    return;
                }

                var signerName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";
                if (string.IsNullOrWhiteSpace(signerName) || signerName == "Unknown User")
                {
                    Logger.LogError("Invalid user name for signing: {UserName}", signerName);
                    Snackbar.Add(Localizer["InvalidUserNameForSigning"], Severity.Error);
                    return;
                }

                CurrentCosigning.IsSigned = true;
                CurrentCosigning.SignerId = signerId;
                CurrentCosigning.SignerName = signerName;
                CurrentCosigning.Date = DateTime.UtcNow;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                if (IsNewRecord)
                {
                    CurrentCosigning.Id = Guid.NewGuid();
                    await CosigningService.AddCosigning(new List<Cosigning> { CurrentCosigning });
                    IsNewRecord = false;
                }
                else
                {
                    await CosigningService.UpdateCosigning(CurrentCosigning);
                }

                Logger.LogInformation("Document signed successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentSignedSuccessfully"], Severity.Success);

                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error signing document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorSigningDocument"], Severity.Error);
            }

        }

        /// <summary>
        /// Handle document cosigning
        /// </summary>
        private async Task CosignDocument()
        {

            try
            {
                Logger.LogInformation("Cosigning document for record {RecordId}", RecordId);

                // Validate required fields
                if (!Guid.TryParse(CurrentUser.id, out var cosignerId) || cosignerId == Guid.Empty)
                {
                    Logger.LogError("Invalid user ID for cosigning: {UserId}", CurrentUser.id);
                    Snackbar.Add(Localizer["InvalidUserForCosigning"], Severity.Error);
                    return;
                }

                var cosignerName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";
                if (string.IsNullOrWhiteSpace(cosignerName) || cosignerName == "Unknown User")
                {
                    Logger.LogError("Invalid user name for cosigning: {UserName}", cosignerName);
                    Snackbar.Add(Localizer["InvalidUserNameForCosigning"], Severity.Error);
                    return;
                }

                CurrentCosigning.IsCosigned = true;
                CurrentCosigning.CosignerId = cosignerId;
                CurrentCosigning.CosignerName = cosignerName;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                await CosigningService.UpdateCosigning(CurrentCosigning);

                Logger.LogInformation("Document cosigned successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentCosignedSuccessfully"], Severity.Success);

                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error cosigning document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorCosigningDocument"], Severity.Error);
            }

        }

        /// <summary>
        /// Get the appropriate color for the status chip
        /// </summary>
        private Color GetStatusColor()
        {
            if (CurrentCosigning.IsLocked) return Color.Warning;
            if (CurrentCosigning.IsCosigned) return Color.Success;
            if (CurrentCosigning.IsSigned) return Color.Info;
            return Color.Default;
        }

        /// <summary>
        /// Get the status text for the status chip
        /// </summary>
        private string GetStatusText()
        {
            if (CurrentCosigning.IsLocked) return Localizer["Locked"];
            if (CurrentCosigning.IsCosigned) return Localizer["Cosigned"];
            if (CurrentCosigning.IsSigned) return Localizer["Signed"];
            return Localizer["PendingSignature"];
        }

        /// <summary>
        /// Public method to refresh the signature status
        /// Can be called from parent components
        /// </summary>
        public async Task RefreshSignatureStatus()
        {
            await LoadCosigningStatus();
            StateHasChanged();
        }

        /// <summary>
        /// Check if the component should be visible based on current state
        /// </summary>
        public bool ShouldShowComponent()
        {
            return ShowCosigningSection && RecordId != Guid.Empty;
        }

        /// <summary>
        /// Get the current signature completion percentage for progress indicators
        /// </summary>
        public int GetSignatureProgress()
        {
            if (CurrentCosigning.IsLocked) return 100;
            if (CurrentCosigning.IsCosigned) return 100;
            if (CurrentCosigning.IsSigned && !RequiresCosignature) return 100;
            if (CurrentCosigning.IsSigned && RequiresCosignature) return 75;
            return 0;
        }

        /// <summary>
        /// Show the inline signing form
        /// </summary>
        private async Task ShowSigningForm()
        {
            try
            {
                // Validate document state before showing form
                if (!ValidateDocumentState("ShowSigningForm"))
                {
                    return;
                }

                // Load provider list for cosigning (only if needed)
                if (CurrentCosigning.IsSigned || RequiresCosignature)
                {
                    await LoadProviderList();
                }

                // Set default mode based on current state
                // If document is already signed, default to cosign mode
                // Otherwise, default to sign mode
                _isCoSignMode = CurrentCosigning.IsSigned && RequiresCosignature;
                _selectedCosigner = null;
                _showSigningForm = true;

                Logger.LogInformation("Showing signing form for record {RecordId}. CoSignMode: {CoSignMode}, IsSigned: {IsSigned}",
                    RecordId, _isCoSignMode, CurrentCosigning.IsSigned);

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error showing signing form for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorShowingSigningForm"], Severity.Error);
            }
        }

        /// <summary>
        /// Cancel the signing process
        /// </summary>
        private void CancelSigning()
        {
            _showSigningForm = false;
            _isCoSignMode = false;
            _selectedCosigner = null;
            StateHasChanged();
        }

        /// <summary>
        /// Process the signature (sign or cosign)
        /// </summary>
        private async Task ProcessSignature()
        {
            // Validate document state before processing
            if (!ValidateDocumentState("ProcessSignature"))
            {
                return;
            }

            try
            {
                // Set processing flag to prevent multiple operations
                IsProcessing = true;
                StateHasChanged();

                // Process based on selected mode
                if (_isCoSignMode)
                {
                    await ProcessCosignature();
                }
                else
                {
                    await ProcessDocumentSigning();
                }

                // Hide the form and refresh the status
                _showSigningForm = false;

                // Trigger the callback to notify parent component
                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);

                // Reload the status to ensure we have the latest data
                await LoadCosigningStatus();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing signature for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorProcessingSignature"], Severity.Error);

                // Reload status to ensure we're in a consistent state
                await LoadCosigningStatus();
            }
            finally
            {
                // Reset processing flag unless document is locked
                IsProcessing = CurrentCosigning.IsLocked;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Process document signing
        /// </summary>
        private async Task ProcessDocumentSigning()
        {
            Logger.LogInformation("Signing document for record {RecordId}", RecordId);

            // Validate required fields
            if (!Guid.TryParse(CurrentUser.id, out var signerId) || signerId == Guid.Empty)
            {
                Logger.LogError("Invalid user ID for signing: {UserId}", CurrentUser.id);
                Snackbar.Add(Localizer["InvalidUserForSigning"], Severity.Error);
                return;
            }

            var signerName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";
            if (string.IsNullOrWhiteSpace(signerName) || signerName == "Unknown User")
            {
                Logger.LogError("Invalid user name for signing: {UserName}", signerName);
                Snackbar.Add(Localizer["InvalidUserNameForSigning"], Severity.Error);
                return;
            }

            var now = DateTime.UtcNow;

            // Update signing information
            CurrentCosigning.IsSigned = true;
            CurrentCosigning.SignerId = signerId;
            CurrentCosigning.SignerName = signerName;
            CurrentCosigning.Date = now;
            CurrentCosigning.LastUpdated = now;
            CurrentCosigning.OrganizationId = OrganizationId;

            // Reset cosigning information when re-signing (allows multiple signing operations)
            CurrentCosigning.IsCosigned = false;
            CurrentCosigning.CosignerId = Guid.Empty;
            CurrentCosigning.CosignerName = string.Empty;
            CurrentCosigning.IsLocked = false;
            CurrentCosigning.Notes = string.Empty;

            try
            {
                if (IsNewRecord)
                {
                    CurrentCosigning.Id = Guid.NewGuid();
                    await CosigningService.AddCosigning(new List<Cosigning> { CurrentCosigning });
                    IsNewRecord = false;
                    Logger.LogInformation("Created new cosigning record for {RecordId}", RecordId);
                }
                else
                {
                    await CosigningService.UpdateCosigning(CurrentCosigning);
                    Logger.LogInformation("Updated existing cosigning record for {RecordId}", RecordId);
                }

                Logger.LogInformation("Document signed successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentSignedSuccessfully"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error during document signing process for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorSigningDocument"], Severity.Error);
                throw; // Re-throw to be handled by ProcessSignature method
            }
        }

        /// <summary>
        /// Process document cosigning
        /// </summary>
        private async Task ProcessCosignature()
        {
            // Validate cosigner selection
            if (_selectedCosigner == null)
            {
                Logger.LogWarning("Cosigning attempted without selecting a cosigner for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["PleaseSelectCosigner"], Severity.Warning);
                return;
            }

            // Validate that document is signed first
            if (!CurrentCosigning.IsSigned)
            {
                Logger.LogWarning("Cosigning attempted on unsigned document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentMustBeSignedFirst"], Severity.Warning);
                return;
            }

            // Validate that we have a valid cosigning record
            if (IsNewRecord || CurrentCosigning.Id == Guid.Empty)
            {
                Logger.LogWarning("Cosigning attempted on new/invalid record for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentMustBeSignedFirst"], Severity.Warning);
                return;
            }

            // Validate that document is not locked
            if (CurrentCosigning.IsLocked)
            {
                Logger.LogWarning("Cosigning attempted on locked document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentIsLocked"], Severity.Warning);
                return;
            }

            Logger.LogInformation("Cosigning document for record {RecordId} with cosigner {CosignerId}",
                RecordId, _selectedCosigner.Id);

            try
            {
                // Update cosigning information
                CurrentCosigning.IsCosigned = true;
                CurrentCosigning.CosignerId = _selectedCosigner.Id;
                CurrentCosigning.CosignerName = _selectedCosigner.UserName ?? $"{_selectedCosigner.FirstName} {_selectedCosigner.LastName}".Trim();
                CurrentCosigning.LastUpdated = DateTime.UtcNow;
                CurrentCosigning.OrganizationId = OrganizationId;

                // Update the record in the backend
                await CosigningService.UpdateCosigning(CurrentCosigning);

                Logger.LogInformation("Document cosigned successfully for record {RecordId} by {CosignerName}",
                    RecordId, CurrentCosigning.CosignerName);
                Snackbar.Add(Localizer["DocumentCosignedSuccessfully"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error during cosigning process for record {RecordId} with cosigner {CosignerId}",
                    RecordId, _selectedCosigner?.Id);
                Snackbar.Add(Localizer["ErrorCosigningDocument"], Severity.Error);
                throw; // Re-throw to be handled by ProcessSignature method
            }
        }

        /// <summary>
        /// Lock the document
        /// </summary>
        private async Task LockDocument()
        {
            // Validate document state before locking
            if (!ValidateDocumentState("LockDocument"))
            {
                return;
            }

            // Validate that we have a valid cosigning record
            if (IsNewRecord || CurrentCosigning.Id == Guid.Empty)
            {
                Logger.LogWarning("Lock attempted on new/invalid record for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentMustBeSignedFirst"], Severity.Warning);
                return;
            }

            // Validate that document is signed
            if (!CurrentCosigning.IsSigned)
            {
                Logger.LogWarning("Lock attempted on unsigned document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentMustBeSignedFirst"], Severity.Warning);
                return;
            }

            // Validate cosignature requirement
            if (RequiresCosignature && !CurrentCosigning.IsCosigned)
            {
                Logger.LogWarning("Lock attempted on document requiring cosignature for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentRequiresCosignature"], Severity.Warning);
                return;
            }

            try
            {
                Logger.LogInformation("Locking document for record {RecordId}", RecordId);

                // Set processing flag to prevent concurrent operations
                IsProcessing = true;
                StateHasChanged();

                // Update locking information
                CurrentCosigning.IsLocked = true;
                CurrentCosigning.LastUpdated = DateTime.UtcNow;

                // Update the record in the backend
                await CosigningService.UpdateCosigning(CurrentCosigning);

                Logger.LogInformation("Document locked successfully for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["DocumentLockedSuccessfully"], Severity.Success);

                // Notify parent component
                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error locking document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLockingDocument"], Severity.Error);

                // Reset processing flag on error
                IsProcessing = false;

                // Reload status to ensure we're in a consistent state
                await LoadCosigningStatus();
            }
        }

        /// <summary>
        /// Get the signature text for display in the primary signature field
        /// </summary>
        private string GetSignatureText()
        {
            var lines = new List<string>();

            if (CurrentCosigning.IsSigned)
            {
                lines.Add($"Electronically Signed by {CurrentCosigning.SignerName} on {CurrentCosigning.Date:MM/dd/yyyy}");
            }

            if (CurrentCosigning.IsCosigned)
            {
                lines.Add($"Electronically Cosigned by {CurrentCosigning.CosignerName} on {CurrentCosigning.LastUpdated:MM/dd/yyyy}");
            }

            if (CurrentCosigning.IsLocked)
            {
                lines.Add("Notes Locked!!");
            }

            return string.Join("\n", lines);
        }

        /// <summary>
        /// Load the provider list for cosigning
        /// </summary>
        private async Task LoadProviderList()
        {
            try
            {
                // Get all members for the organization
                var allMembers = await MemberService.GetAllMembersAsync(OrganizationId, Subscription);

                // Filter to only include providers (non-patients) who are active
                _providerList = allMembers
                    .Where(m => m.IsActive &&
                           m.RoleName != null &&
                           !m.RoleName.Equals("Patient", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                Logger.LogInformation("Loaded {Count} providers for cosigning selection", _providerList.Count);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading provider list for organization {OrganizationId}", OrganizationId);
                _providerList = new List<Member>();
            }
        }

        /// <summary>
        /// Check if the document can be signed
        /// </summary>
        private bool CanSign()
        {
            return !CurrentCosigning.IsLocked && !IsProcessing;
        }

        /// <summary>
        /// Check if the document can be cosigned
        /// </summary>
        private bool CanCosign()
        {
            return !CurrentCosigning.IsLocked &&
                   !IsProcessing &&
                   CurrentCosigning.IsSigned &&
                   !IsNewRecord &&
                   RequiresCosignature;
        }

        /// <summary>
        /// Check if the document can be locked
        /// </summary>
        private bool CanLock()
        {
            return !CurrentCosigning.IsLocked &&
                   !IsProcessing &&
                   CurrentCosigning.IsSigned &&
                   (!RequiresCosignature || CurrentCosigning.IsCosigned);
        }

        /// <summary>
        /// Get the current signing status for display
        /// </summary>
        private string GetSigningStatus()
        {
            if (CurrentCosigning.IsLocked)
                return "Locked";

            if (CurrentCosigning.IsSigned && CurrentCosigning.IsCosigned)
                return "Signed and Cosigned";

            if (CurrentCosigning.IsSigned)
                return RequiresCosignature ? "Signed (Awaiting Cosignature)" : "Signed";

            return "Unsigned";
        }

        /// <summary>
        /// Get the status color for the chip display
        /// </summary>
        

        /// <summary>
        /// Validate document state before performing operations
        /// </summary>
        private bool ValidateDocumentState(string operation)
        {
            // Check if document is locked
            if (CurrentCosigning.IsLocked)
            {
                Logger.LogWarning("{Operation} attempted on locked document for record {RecordId}", operation, RecordId);
                Snackbar.Add(Localizer["DocumentIsLocked"], Severity.Warning);
                return false;
            }

            // Check if operation is already in progress
            if (IsProcessing)
            {
                Logger.LogWarning("{Operation} attempted while operation in progress for record {RecordId}", operation, RecordId);
                Snackbar.Add(Localizer["SignatureOperationInProgress"], Severity.Warning);
                return false;
            }

            // Check if RecordId is valid
            if (RecordId == Guid.Empty)
            {
                Logger.LogWarning("{Operation} attempted with invalid RecordId", operation);
                Snackbar.Add(Localizer["InvalidRecord"], Severity.Error);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Refresh the component state and reload data
        /// </summary>
        public async Task RefreshAsync()
        {
            try
            {
                await LoadCosigningStatus();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error refreshing cosigning component for record {RecordId}", RecordId);
            }
        }
    }
}