﻿/* Professional Cosigning Component Styles */

.cosigning-container {
    margin: 16px 0;
    width: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.cosigning-paper {
    border-radius: 8px !important;
    border: 1px solid #e1e5e9 !important;
    background: #ffffff !important;
    padding: 24px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
    transition: box-shadow 0.2s ease !important;
}

    .cosigning-paper:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
    }

/* Header Section */
.cosigning-header {
    margin-bottom: 20px !important;
    padding-bottom: 16px !important;
    border-bottom: 1px solid #e1e5e9 !important;
}

.header-icon {
    color: #3b82f6 !important;
}

.header-title {
    color: #2d3748 !important;
    font-weight: 600 !important;
    margin: 0 !important;
    font-size: 1.125rem !important;
}

.status-chip {
    font-size: 0.75rem !important;
    height: 24px !important;
    font-weight: 500 !important;
}

/* Patient Information Section */
.patient-info-section {
    margin-bottom: 20px !important;
    padding: 16px !important;
    background: #f7fafc !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
}

.patient-info-row {
    gap: 8px !important;
}

.info-label {
    color: #4a5568 !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
}

.info-value {
    color: #2d3748 !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
}

/* Signature Section */
.signature-section {
    gap: 16px !important;
}

.signature-block {
    padding: 20px !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    background: #ffffff !important;
    transition: all 0.2s ease !important;
}

    .signature-block:hover {
        border-color: #3182ce !important;
        box-shadow: 0 2px 8px rgba(49, 130, 206, 0.1) !important;
    }

    .signature-block.signed {
        border: 1px solid #38a169 !important;
        background: #f0fff4 !important;
    }

        .signature-block.signed:hover {
            border-color: #2f855a !important;
        }

.signature-title {
    color: #2d3748 !important;
    font-weight: 600 !important;
    margin: 0 !important;
    font-size: 0.875rem !important;
}

    .signature-title.signed {
        color: #2f855a !important;
    }

.signature-button {
    min-width: 120px !important;
    height: 36px !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    text-transform: none !important;
    font-size: 0.875rem !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease !important;
}

    .signature-button:hover {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
    }

    .signature-button:disabled {
        box-shadow: none !important;
    }

.signature-divider {
    margin: 12px 0 !important;
    background-color: #e2e8f0 !important;
}

/* Lock Status */
.lock-alert {
    border-radius: 6px !important;
    background: #ebf8ff !important;
    border: 1px solid #90cdf4 !important;
    margin-top: 16px !important;
}

font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cosigning-paper {
        padding: 16px !important;
        margin: 12px 0 !important;
    }

    .signature-button {
        width: 100% !important;
        min-width: unset !important;
    }
}

@media (max-width: 480px) {
    .cosigning-paper {
        padding: 12px !important;
    }

    .header-title {
        font-size: 1rem !important;
    }

    .patient-info-section {
        padding: 12px !important;
    }

    .signature-block {
        padding: 16px !important;
    }
}

/* Focus states for accessibility */
.signature-button:focus {
    outline: 2px solid #3182ce !important;
    outline-offset: 2px !important;
}
